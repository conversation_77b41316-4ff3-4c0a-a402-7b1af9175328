<!-- player.component.html -->
<div class="player-container">
  <!-- Error overlay - shown when there's a playback error or device validation error -->
  <div *ngIf="playbackError || deviceValidationError" class="error-overlay">
    <div class="error-container">
      <div class="error-icon">
        <span class="material-icons">{{ deviceValidationError ? 'error_outline' : 'display_settings' }}</span>
      </div>
      <h2 class="error-title">{{ deviceValidationError ? 'Device Not Found' : 'Display Not Ready' }}</h2>
      <p class="error-message">
        {{ deviceValidationError ? 'This device is no longer registered in the system.' : 'No content has been assigned to this display yet.' }}
      </p>
      <p class="error-submessage">
        {{ deviceValidationError ? 'The device may have been removed by an administrator.' : 'Please check with your administrator or try the options below.' }}
      </p>
      
      <div class="error-actions">
        <button *ngIf="!deviceValidationError" class="action-button primary" (click)="reloadPlaylist()">
          <span class="material-icons">refresh</span>
          Check for Content
        </button>
        <button class="action-button secondary" (click)="resetDevice()">
          <span class="material-icons">restart_alt</span>
          Reset Device
        </button>
      </div>
    </div>
  </div>


  <!-- Main content container with carousel -->
  <div class="content-container">
    <!-- Carousel wrapper -->
    <div class="carousel-wrapper" [class.transitioning]="isTransitioning">
      <!-- Current content slide -->
      <div class="carousel-slide current-slide"
           [style.transform]="'translateX(' + currentSlideOffset + '%)'">
        <div *ngIf="currentItem" class="content-item">
          <ng-container [ngSwitch]="currentItem.type">
            <!-- Image content -->
            <app-image-item
              *ngSwitchCase="'image'"
              [item]="currentItem"
              [scaling]="'stretch'"
              (ended)="onContentEnded()">
            </app-image-item>

            <!-- Video content -->
            <app-video-item
              *ngSwitchCase="'video'"
              [item]="currentItem"
              [scaling]="'stretch'"
              [muted]="currentItem?.settings?.muted ?? true"
              [loop]="currentItem?.settings?.loop ?? false"
              (ended)="onContentEnded()">
            </app-video-item>

            <!-- Web content -->
            <app-web-item
              *ngSwitchCase="'webpage'"
              [item]="currentItem"
              [duration]="currentItem?.duration || 10"
              (ended)="onContentEnded()">
            </app-web-item>

            <!-- Ticker/text content -->
            <app-ticker-item
              *ngSwitchCase="'ticker'"
              [item]="currentItem"
              [duration]="currentItem?.duration || 10"
              (ended)="onContentEnded()">
            </app-ticker-item>

            <!-- Fallback for unknown content type -->
            <div *ngSwitchDefault class="fallback-content">
              <span class="material-icons">image_not_supported</span>
              <p>Unsupported content type</p>
            </div>
          </ng-container>
        </div>
      </div>

      <!-- Next content slide -->
      <div class="carousel-slide next-slide"
           [style.transform]="'translateX(' + nextSlideOffset + '%)'">
        <div *ngIf="nextItem" class="content-item">
          <ng-container [ngSwitch]="nextItem.type">
            <!-- Image content -->
            <app-image-item
              *ngSwitchCase="'image'"
              [item]="nextItem"
              [scaling]="'stretch'"
              [preload]="true">
            </app-image-item>

            <!-- Video content -->
            <app-video-item
              *ngSwitchCase="'video'"
              [item]="nextItem"
              [scaling]="'stretch'"
              [muted]="nextItem?.settings?.muted ?? true"
              [loop]="nextItem?.settings?.loop ?? false"
              [preload]="true">
            </app-video-item>

            <!-- Web content -->
            <app-web-item
              *ngSwitchCase="'webpage'"
              [item]="nextItem"
              [duration]="nextItem?.duration || 10"
              [preload]="true">
            </app-web-item>

            <!-- Ticker/text content -->
            <app-ticker-item
              *ngSwitchCase="'ticker'"
              [item]="nextItem"
              [duration]="nextItem?.duration || 10"
              [preload]="true">
            </app-ticker-item>

            <!-- Fallback for unknown content type -->
            <div *ngSwitchDefault class="fallback-content">
              <span class="material-icons">image_not_supported</span>
              <p>Unsupported content type</p>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>

  <!-- Carousel progress indicator -->
  <div *ngIf="currentPlayerState && currentPlayerState.totalItems > 1" class="carousel-indicators">
    <div class="progress-dots">
      <div *ngFor="let item of getProgressDots(); let i = index"
           class="progress-dot"
           [class.active]="i === currentPlayerState.currentItemIndex"
           [class.transitioning]="isTransitioning && i === getNextItemIndex()">
      </div>
    </div>
  </div>

  <!-- Diagnostic overlay -->
  <div *ngIf="playerState$ | async as state" class="diagnostics-overlay">
    <div class="info-pill">
      <!-- <div class="status-indicator" [class.online]="isOnline" [class.offline]="!isOnline"></div>
      <span class="playlist-name">{{ state.currentPlaylistName }}</span>
      <span class="item-counter">{{ state.currentItemIndex + 1 }}/{{ state.totalItems }}</span>
      <button class="control-button refresh-button" (click)="reloadPlaylist()" title="Reload Playlist">
        <span class="material-icons">refresh</span>
      </button> -->


    </div>
  </div>
</div>