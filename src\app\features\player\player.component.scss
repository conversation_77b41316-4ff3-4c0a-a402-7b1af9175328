/* player.component.scss */
:host {
  display: block;
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  overflow: hidden;
  background-color: #000;
  position: fixed;
  top: 0;
  left: 0;
  margin: 0 !important;
  padding: 0 !important;
}

.player-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  margin: 0 !important;
  padding: 0 !important;
}

.content-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  margin: 0 !important;
  padding: 0 !important;
}

/* Carousel wrapper and slides */
.carousel-wrapper {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  margin: 0 !important;
  padding: 0 !important;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden;
  transition: transform 0.8s cubic-bezier(0.4, 0.0, 0.2, 1);
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.carousel-wrapper.transitioning .carousel-slide {
  transition: transform 0.8s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.current-slide {
  z-index: 10;
}

.next-slide {
  z-index: 9;
}

.content-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden;
}


// Error overlay styles - now friendly and welcoming
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin: 0 !important;
  padding: 0 !important;
}

.error-container {
  text-align: center;
  padding: 3rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  max-width: 80%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  .friendly-icon {
    font-size: 5rem;
    color: #64b5f6;
    margin-bottom: 1.5rem;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  }
  
  h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
    font-weight: 300;
    color: #ffffff;
  }
  
  .friendly-message {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #e3f2fd;
  }
  
  .recovery-message {
    margin-top: 1rem;
    font-style: italic;
    opacity: 0.8;
    color: #bbdefb;
    font-size: 0.9rem;
  }
  
  .reload-button {
    margin-top: 2rem;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    padding: 0.875rem 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    margin-left: auto;
    margin-right: auto;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    font-size: 0.95rem;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

// Offline banner
.offline-banner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 0.5rem 1rem;
  background-color: rgba(255, 193, 7, 0.9);
  color: #212529;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  z-index: 90;
  font-size: 0.875rem;
  font-weight: 500;
  
  .material-icons {
    font-size: 1rem;
  }
}

// Fallback content
.fallback-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  
  .material-icons {
    font-size: 4rem;
    margin-bottom: 1rem;
  }
}

/* Carousel progress indicators */
.carousel-indicators {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 50;
  pointer-events: none;
}

.progress-dots {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
}

.progress-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;

  &.active {
    background-color: rgba(255, 255, 255, 0.9);
    transform: scale(1.2);
  }

  &.transitioning {
    background-color: rgba(255, 255, 255, 0.6);
    transform: scale(1.1);
  }
}

// Diagnostic overlay
.diagnostics-overlay {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  z-index: 50;
}

.info-pill {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  backdrop-filter: blur(4px);
  
  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    
    &.online {
      background-color: #10b981; // green
    }
    
    &.offline {
      background-color: #ef4444; // red
    }
  }
  
  .playlist-name {
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* Control buttons */
.control-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  margin-left: 0.5rem;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  transition: opacity 0.2s, transform 0.2s;
  
  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.refresh-button {
  color: #10b981; // green
}

/* Fullscreen styles */
:fullscreen .player-container,
:-webkit-full-screen .player-container,
:-moz-full-screen .player-container,
:-ms-fullscreen .player-container {
  width: 100vw;
  height: 100vh;
}

/* Fix for Safari and iOS */
.player-container:fullscreen {
  width: 100vw !important;
  height: 100vh !important;
}