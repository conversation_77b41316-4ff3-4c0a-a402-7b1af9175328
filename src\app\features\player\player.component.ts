// player.component.ts
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON>roy, HostListener, ElementRef, NgZone, PLATFORM_ID, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { isPlatformBrowser } from '@angular/common';
import { Router } from '@angular/router';
import { Observable, Subscription, interval, fromEvent, of } from 'rxjs';
import { switchMap, catchError, tap } from 'rxjs/operators';
import { PlaybackService } from '../../core/services/playback.service';
import { ScheduleService } from '../../core/services/schedule.service';
import { HeartbeatService } from '../../core/services/heartbeat.service';
import { LogService } from '../../core/services/log.service';
import { SupabaseApiService } from '../../core/services/supabase-api.service';
import { WakeLockService } from '../../core/services/wake-lock.service';
import { PlaylistItem } from '../../core/models/playlist.model';
import { PlayerState } from '../../core/models/player-state.model';
import { ImageItemComponent } from './components/image-item.component';
import { VideoItemComponent } from './components/video-item.component';
import { WebItemComponent } from './components/web-item.component';
import { TickerItemComponent } from './components/ticker-item.component';

@Component({
  selector: 'app-player',
  standalone: true,
  imports: [
    CommonModule,
    ImageItemComponent, 
    VideoItemComponent, 
    WebItemComponent, 
    TickerItemComponent
  ],
  templateUrl: './player.component.html',
  styleUrls: ['./player.component.scss']
})
export class PlayerComponent implements OnInit, OnDestroy {
  currentItem: PlaylistItem | null = null;
  nextItem: PlaylistItem | null = null;
  playerState$: Observable<PlayerState>;
  currentPlayerState: PlayerState | null = null;
  playbackError: string | null = null;
  isFullscreen = false;
  isOnline = typeof window !== 'undefined' ? navigator.onLine : false;
  deviceValidationError: boolean = false;
  private isBrowser: boolean;

  // Carousel state
  isTransitioning = false;
  currentSlideOffset = 0;
  nextSlideOffset = 100;
  
  private lastTimeCheck: number = 0;
  private preciseMinuteInterval: any = null;
  private transitionTimeoutIds: any[] = [];
  private forceReloadTimeoutId: any = null;
  
  private subscriptions: Subscription[] = [];
  private heartbeatInterval: Subscription | null = null;
  private scheduleCheckInterval: Subscription | null = null;

  constructor(
    private playbackService: PlaybackService,
    private scheduleService: ScheduleService,
    private heartbeatService: HeartbeatService,
    private logService: LogService,
    private supabaseApi: SupabaseApiService,
    private wakeLockService: WakeLockService,
    private router: Router,
    private elementRef: ElementRef,
    private zone: NgZone,
    @Inject(PLATFORM_ID) private platformId: any
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    this.playerState$ = this.playbackService.playerState$;
    this.logService.setDebugLevel(0); // Set to lowest level for maximum verbosity
    this.logService.info('Player component initialized with verbose logging');
  }

  ngOnInit(): void {
    // First validate device exists in database
    this.validateDevice().then(isValid => {
      if (!isValid) {
        this.deviceValidationError = true;
        this.playbackError = 'Device not found in system';
        this.logService.error('Device validation failed - device not found in database');
        return;
      }
      
      // Device is valid, proceed with normal initialization
      this.setupNetworkListeners();
      this.setupTimeChangeListeners();
      this.setupPlayback();
      this.setupScheduleChecking();
      this.setupPeriodicDeviceValidation();
      this.startHeartbeat();
      
      // Ensure we're in fullscreen mode
      this.enterFullscreen();
      
      // Request wake lock to keep screen always on
      this.requestWakeLock();
      
      // Log player startup
      this.logService.info('Player started');
      
      // Subscribe to schedule changes directly
      const scheduleChangeSub = this.scheduleService.scheduleChange$.subscribe(playlistId => {
        this.logService.info(`Schedule change received in player component: ${playlistId}`);
        // Force a complete reload to ensure the view updates
        this.forceReloadPlaylist(playlistId);
      });
      
      this.subscriptions.push(scheduleChangeSub);
    });
  }

  ngOnDestroy(): void {
    // Clear all transition timeouts
    this.transitionTimeoutIds.forEach(id => clearTimeout(id));
    
    if (this.forceReloadTimeoutId) {
      clearTimeout(this.forceReloadTimeoutId);
    }
    
    // Release wake lock
    this.wakeLockService.releaseWakeLock();
    
    // Clean up all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
    if (this.heartbeatInterval) {
      this.heartbeatInterval.unsubscribe();
    }
    if (this.scheduleCheckInterval) {
      this.scheduleCheckInterval.unsubscribe();
    }
    if (this.preciseMinuteInterval) {
      clearInterval(this.preciseMinuteInterval);
    }
    this.logService.info('Player stopped');
  }
  
  // Handle key presses (useful for debugging/admin functions)
  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    // Diagnostic key shortcuts (only active in development)
    if (event.altKey && event.key === 'd') {
      this.router.navigate(['/diagnostics']);
    }
    
    // Toggle fullscreen with F key
    if (event.key === 'f') {
      this.toggleFullscreen();
    }
    
    // Exit fullscreen with Escape key
    if (event.key === 'Escape') {
      this.exitFullscreen();
    }
    
    // Force reload the current playlist with R key
    if (event.key === 'r') {
      this.playbackService.reloadPlaylist();
    }
  }
  
  // Toggle fullscreen on any mouse click or touch
  @HostListener('document:click')
  onDocumentClick(): void {
    this.toggleFullscreen();
  }

  private setupNetworkListeners(): void {
    // Only setup network listeners in browser environment
    if (!this.isBrowser) return;
    
    // Listen for online/offline events
    const onlineSubscription = fromEvent(window, 'online').subscribe(() => {
      this.isOnline = true;
      this.logService.info('Network connection restored');
      
      // Force reload playlist when we come back online
      this.playbackService.reloadPlaylist();
    });
    
    const offlineSubscription = fromEvent(window, 'offline').subscribe(() => {
      this.isOnline = false;
      this.logService.warn('Network connection lost');
    });
    
    this.subscriptions.push(onlineSubscription, offlineSubscription);
  }

  private setupTimeChangeListeners(): void {
    // Only setup time change listeners in browser environment
    if (!this.isBrowser) return;
    
    // Check when visibility changes (tab becomes active)
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.logService.info('Tab became visible, checking schedule');
        this.checkScheduleAndReload();
      }
    });
    
    // Listen for time adjustments (not perfect but helps)
    const timeCheckInterval = setInterval(() => {
      const now = new Date();
      const nowSecs = Math.floor(now.getTime() / 1000);
      
      if (!this.lastTimeCheck) {
        this.lastTimeCheck = nowSecs;
      } else {
        // If more than 90 seconds have passed since our last 60-second check,
        // the clock might have been adjusted
        const diff = nowSecs - this.lastTimeCheck;
        if (diff > 90 || diff < 30) {
          this.logService.info(`Time jump detected (${diff}s), checking schedule`);
          this.checkScheduleAndReload();
        }
        this.lastTimeCheck = nowSecs;
      }
    }, 60000);
    
    // Fix the subscription push by creating a proper Subscription
    const subscription = new Subscription();
    subscription.add(() => clearInterval(timeCheckInterval));
    this.subscriptions.push(subscription);
  }

  private setupPlayback(): void {
    // Subscribe to current item changes
    const currentItemSub = this.playbackService.currentItem$.subscribe(item => {
      // Wrap in NgZone to ensure change detection
      this.zone.run(() => {
        if (item && item !== this.currentItem) {
          // If we have a current item, prepare for carousel transition
          if (this.currentItem) {
            this.prepareCarouselTransition(item);
          } else {
            // First item, no transition needed
            this.currentItem = item;
            this.logService.info(`Initial item loaded: ${item.name}`);
          }
        } else if (!item) {
          // Handle null item (reset)
          this.currentItem = null;
          this.nextItem = null;
          this.resetCarouselState();
        }
      });
    });

    // Subscribe to next item changes
    const nextItemSub = this.playbackService.nextItem$.subscribe(item => {
      this.zone.run(() => {
        this.nextItem = item;
        if (item) {
          this.logService.info(`Next item prepared: ${item.name}`);
        }
      });
    });

    // Subscribe to errors
    const errorSub = this.playbackService.playbackError$.subscribe(error => {
      this.zone.run(() => {
        this.playbackError = error;
        if (error) {
          this.logService.error(`Playback error: ${error}`);
        }
      });
    });

    // Subscribe to player state
    const playerStateSub = this.playerState$.subscribe(state => {
      this.zone.run(() => {
        this.currentPlayerState = state;
      });
    });

    this.subscriptions.push(currentItemSub, nextItemSub, errorSub, playerStateSub);

    // Start playback
    this.playbackService.startPlayback();
  }

  private setupScheduleChecking(): void {
    // Initial check on startup with a small delay to ensure everything is loaded
    setTimeout(() => {
      this.logService.info('Performing initial schedule check');
      this.checkScheduleAndReload();
    }, 3000);
    
    // Check more frequently - every 15 seconds instead of every minute
    this.scheduleCheckInterval = interval(15000).subscribe(() => {
      const now = new Date();
      this.logService.debug(`Schedule check at ${now.toTimeString()}`);
      this.checkScheduleAndReload();
    });
    
    // Setup more precise checks exactly at scheduled times
    this.setupTargetedScheduleChecks();
    
    // Also add precise minute boundary checks
    this.setupPreciseMinuteChecks();
  }

  // Helper method to check schedule and reload if needed
  // Helper method to check schedule and reload if needed
  private checkScheduleAndReload(): void {
    this.scheduleService.checkSchedule().subscribe(
      (changed: boolean) => {
        if (changed) {
          this.logService.info('Schedule changed, reloading playlist');
          // Note: We no longer need to call reloadPlaylist directly here
          // because we're already reacting to the scheduleChange$ events
        } else {
          this.logService.debug('No schedule changes detected');
        }
      },
      (error: any) => {
        this.logService.error(`Schedule check error: ${error}`);
      }
    );
  }

  // New method to set up targeted checks at known schedule times
  private setupTargetedScheduleChecks(): void {
    // First clear any existing timeouts
    this.transitionTimeoutIds.forEach(id => clearTimeout(id));
    this.transitionTimeoutIds = [];
    
    // Only proceed if in browser environment
    if (!this.isBrowser) return;
    
    // Get all scheduled playlists and set up targeted checks for each transition time
    const deviceId = localStorage.getItem('deviceId');
    if (!deviceId) return;
    
    this.logService.info('Setting up targeted schedule checks');
    
    // Get screen configuration with schedules
    this.supabaseApi.getScreenById(deviceId).subscribe(screen => {
      if (!screen || !screen.schedule || !screen.schedule.upcoming) {
        this.logService.warn('No schedules found for targeted checks');
        return;
      }
      
      // Extract all unique transition times
      const transitionTimes = new Set<string>();
      screen.schedule.upcoming.forEach(schedule => {
        transitionTimes.add(schedule.start_time);
        transitionTimes.add(schedule.end_time);
      });
      
      this.logService.info(`Found ${transitionTimes.size} unique transition times to monitor`);
      
      // For each transition time, set up a check 5 seconds before and after the scheduled time
      transitionTimes.forEach(timeStr => {
        this.setupTransitionTimeChecks(timeStr);
      });
    });
  }

  // New method to set up checks for a specific schedule transition time
  private setupTransitionTimeChecks(timeStr: string): void {
    const [hours, minutes] = timeStr.split(':').map(Number);
    
    // Calculate milliseconds until the next occurrence of this time
    const calculateMsToTime = (): number => {
      const now = new Date();
      const target = new Date();
      
      target.setHours(hours, minutes, 0, 0);
      
      // If the target time is already passed for today, schedule for tomorrow
      if (now > target) {
        target.setDate(target.getDate() + 1);
      }
      
      return target.getTime() - now.getTime();
    };
    
    // Set up targeted checks for this time
    const scheduleNextCheck = () => {
      const msToTime = calculateMsToTime();
      
      // Schedule check 10 seconds before the transition time
      const beforeCheck = setTimeout(() => {
        this.logService.info(`Pre-transition check for ${timeStr}`);
        this.checkScheduleAndReload();
      }, msToTime - 10000);
      
      // Schedule check exactly at the transition time
      const exactCheck = setTimeout(() => {
        this.logService.info(`Exact transition check for ${timeStr}`);
        this.checkScheduleAndReload();
      }, msToTime);
      
      // Schedule check 10 seconds after the transition time
      const afterCheck = setTimeout(() => {
        this.logService.info(`Post-transition check for ${timeStr}`);
        this.checkScheduleAndReload();
        
        // Set up the next day's check
        scheduleNextCheck();
      }, msToTime + 10000);
      
      // Store the timeouts so they can be cleared if needed
      this.transitionTimeoutIds.push(beforeCheck, exactCheck, afterCheck);
    };
    
    // Start the process
    scheduleNextCheck();
    this.logService.info(`Set up transition checks for time: ${timeStr}`);
  }

  // Add precise timing checks at minute boundaries
  private setupPreciseMinuteChecks(): void {
    // Check how many milliseconds until the next minute boundary
    const now = new Date();
    const msToNextMinute = (60 - now.getSeconds()) * 1000 - now.getMilliseconds();
    
    // Set up a timeout to check at the next minute boundary
    setTimeout(() => {
      const exactMinute = new Date();
      this.logService.info(`Precise minute check at ${exactMinute.toTimeString()}`);
      this.checkScheduleAndReload();
      
      // Then set up an interval to check every minute precisely at the minute boundary
      this.preciseMinuteInterval = setInterval(() => {
        const nextExactMinute = new Date();
        this.logService.info(`Precise minute check at ${nextExactMinute.toTimeString()}`);
        this.checkScheduleAndReload();
      }, 60000);
    }, msToNextMinute);
  }

  // Toggle fullscreen
  toggleFullscreen(): void {
    if (this.isFullscreen) {
      this.exitFullscreen();
    } else {
      this.enterFullscreen();
    }
  }

  // Enhanced fullscreen method with better cross-browser support
  enterFullscreen(): void {
    const elem = document.documentElement;
    
    try {
      this.logService.info('Attempting to enter fullscreen mode');
      
      if (elem.requestFullscreen) {
        elem.requestFullscreen();
      } else if ((elem as any).mozRequestFullScreen) { // Firefox
        (elem as any).mozRequestFullScreen();
      } else if ((elem as any).webkitRequestFullscreen) { // Chrome, Safari and Opera
        (elem as any).webkitRequestFullscreen();
      } else if ((elem as any).msRequestFullscreen) { // IE/Edge
        (elem as any).msRequestFullscreen();
      }
      
      this.isFullscreen = true;
      this.logService.info('Entered fullscreen mode');
    } catch (error) {
      this.logService.warn(`Could not enter fullscreen mode: ${error}`);
    }
  }

  // Method to exit fullscreen
  exitFullscreen(): void {
    try {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if ((document as any).mozCancelFullScreen) { // Firefox
        (document as any).mozCancelFullScreen();
      } else if ((document as any).webkitExitFullscreen) { // Chrome, Safari and Opera
        (document as any).webkitExitFullscreen();
      } else if ((document as any).msExitFullscreen) { // IE/Edge
        (document as any).msExitFullscreen();
      }
      
      this.isFullscreen = false;
      this.logService.info('Exited fullscreen mode');
    } catch (error) {
      this.logService.warn(`Could not exit fullscreen mode: ${error}`);
    }
  }

  // Track fullscreen state changes
  @HostListener('document:fullscreenchange', ['$event'])
  @HostListener('document:webkitfullscreenchange', ['$event'])
  @HostListener('document:mozfullscreenchange', ['$event'])
  @HostListener('document:MSFullscreenChange', ['$event'])
  onFullscreenChange(): void {
    // Update the fullscreen state based on the document's fullscreen status
    this.isFullscreen = !!document.fullscreenElement || 
                       !!(document as any).webkitFullscreenElement || 
                       !!(document as any).mozFullScreenElement || 
                       !!(document as any).msFullscreenElement;
    
    this.logService.info(`Fullscreen state changed: ${this.isFullscreen ? 'enabled' : 'disabled'}`);
  }

  // Force a much more aggressive reload of a playlist
  private forceReloadPlaylist(playlistId: string): void {
    // Cancel any existing reload
    if (this.forceReloadTimeoutId) {
      clearTimeout(this.forceReloadTimeoutId);
    }
    
    // Reset component state
    this.zone.run(() => {
      this.currentItem = null;
    });
    
    // Allow time for the reset to take effect in the UI
    this.forceReloadTimeoutId = setTimeout(() => {
      this.zone.run(() => {
        this.logService.info(`Force reloading playlist: ${playlistId}`);
        this.playbackService.loadPlaylist(playlistId);
      });
    }, 300); // Give time for the UI to refresh
  }

  private async requestWakeLock(): Promise<void> {
    this.logService.info('🔥 INITIATING NEVER-SLEEP MODE FOR DIGITAL SIGNAGE! 🔥');
    
    const success = await this.wakeLockService.requestWakeLock();
    if (success) {
      this.logService.info('🚀 AGGRESSIVE WAKE LOCK ACTIVATED - SCREEN WILL NEVER SLEEP! 🚀');
      
      // Set up monitoring to ensure wake lock stays active
      this.setupWakeLockMonitoring();
    } else {
      this.logService.error('❌ CRITICAL: Failed to activate keep-alive methods - screen may sleep!');
      
      // Try to reactivate after a short delay
      setTimeout(() => {
        this.requestWakeLock();
      }, 5000);
    }
  }

  private setupWakeLockMonitoring(): void {
    // Monitor wake lock status every 30 seconds
    const monitorInterval = setInterval(() => {
      const status = this.wakeLockService.getWakeLockStatus();
      
      if (!status.active) {
        this.logService.warn('⚠️ ALERT: Wake lock is not active - reactivating immediately!');
        this.wakeLockService.forceReactivate();
      } else {
        this.logService.debug(`✅ Wake lock monitoring: ${status.methods.length} methods active`);
      }
    }, 30000);

    // Store the interval so we can clear it on destroy
    this.subscriptions.push({
      unsubscribe: () => clearInterval(monitorInterval)
    } as any);
  }

  private startHeartbeat(): void {
    // Send heartbeat immediately on startup
    this.sendHeartbeat();
    
    // Send heartbeat every 60 seconds
    this.heartbeatInterval = interval(60000).subscribe(() => {
      this.sendHeartbeat();
    });
  }
  
  private sendHeartbeat(): void {
    const wakeLockStatus = this.wakeLockService.getWakeLockStatus();
    
    this.heartbeatService.sendHeartbeat({
      status: this.playbackError ? 'error' : this.isPlaying() ? 'playing' : 'paused',
      currentItem: this.currentItem?.id,
      currentPlaylist: this.currentPlayerState?.currentPlaylistId || null,
      error: this.playbackError,
      wakeLockActive: wakeLockStatus.active,
      wakeLockSupported: wakeLockStatus.supported,
      wakeLockMethods: wakeLockStatus.methods.join(', ')
    }).subscribe(
      success => {
        if (success) {
          this.logService.debug(`Heartbeat sent - Wake lock: ${wakeLockStatus.active ? 'ACTIVE' : 'INACTIVE'} (${wakeLockStatus.methods.length} methods)`);
        } else {
          this.logService.warn('Heartbeat failed');
        }
      },
      error => {
        this.logService.error(`Heartbeat error: ${error}`);
      }
    );
  }
  
  private isPlaying(): boolean {
    return this.currentPlayerState?.isPlaying || false;
  }

  // Methods to handle manual controls if needed
  skipToNext(): void {
    this.playbackService.skipToNext();
  }

  restartPlayback(): void {
    this.playbackService.restartPlayback();
  }

  // Handle content ended event
  onContentEnded(): void {
    this.logService.info('Content ended, triggering carousel transition');
    this.skipToNext();
  }

  // Carousel transition methods
  private prepareCarouselTransition(newItem: PlaylistItem): void {
    this.logService.info(`Preparing carousel transition to: ${newItem.name}`);

    // Set the new item as next item
    this.nextItem = newItem;

    // Reset slide positions for transition
    this.currentSlideOffset = 0;
    this.nextSlideOffset = 100;

    // Start transition after a brief delay to ensure next content is ready
    setTimeout(() => {
      this.executeCarouselTransition();
    }, 100);
  }

  private executeCarouselTransition(): void {
    if (!this.nextItem) {
      this.logService.warn('No next item available for transition');
      return;
    }

    this.logService.info('Executing carousel transition');
    this.isTransitioning = true;

    // Animate slides
    this.currentSlideOffset = -100; // Current slide moves left
    this.nextSlideOffset = 0;       // Next slide moves to center

    // After transition completes, swap the items
    setTimeout(() => {
      this.completeCarouselTransition();
    }, 800); // Match CSS transition duration
  }

  private completeCarouselTransition(): void {
    this.logService.info('Completing carousel transition');

    // Swap current and next items
    this.currentItem = this.nextItem;
    this.nextItem = null;

    // Reset slide positions
    this.currentSlideOffset = 0;
    this.nextSlideOffset = 100;

    // Clear transition state
    this.isTransitioning = false;

    this.logService.info(`Carousel transition completed. Now playing: ${this.currentItem?.name}`);
  }

  private resetCarouselState(): void {
    this.isTransitioning = false;
    this.currentSlideOffset = 0;
    this.nextSlideOffset = 100;
  }

  // Helper methods for progress indicators
  getProgressDots(): any[] {
    if (!this.currentPlayerState || !this.currentPlayerState.totalItems) {
      return [];
    }
    return new Array(this.currentPlayerState.totalItems);
  }

  getNextItemIndex(): number {
    if (!this.currentPlayerState || !this.currentPlayerState.totalItems) {
      return 0;
    }
    return (this.currentPlayerState.currentItemIndex + 1) % this.currentPlayerState.totalItems;
  }
  
  // Force reload the current playlist
  reloadPlaylist(): void {
    if (this.currentPlayerState?.currentPlaylistId) {
      this.forceReloadPlaylist(this.currentPlayerState.currentPlaylistId);
    } else {
      this.playbackService.reloadPlaylist();
    }
  }

  // Reset device registration and return to registration screen
  resetDevice(): void {
    this.logService.info('Resetting device registration');
    
    // Clear all device registration data from localStorage
    if (this.isBrowser) {
      localStorage.removeItem('deviceId');
      localStorage.removeItem('registrationCode');
      localStorage.removeItem('deviceName');
      localStorage.removeItem('registrationData');
    }
    
    // Stop all services
    this.ngOnDestroy();
    
    // Navigate to registration screen
    this.router.navigate(['/registration']);
  }

  // Validate that the device still exists in the database
  private async validateDevice(): Promise<boolean> {
    if (!this.isBrowser) {
      return true; // Skip validation during SSR
    }

    const deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
      this.logService.warn('No device ID found in localStorage');
      return false;
    }

    try {
      const screen = await this.supabaseApi.getScreenById(deviceId).toPromise();
      if (!screen) {
        this.logService.error(`Device ${deviceId} not found in database`);
        return false;
      }
      
      this.logService.info(`Device ${deviceId} validated successfully`);
      return true;
    } catch (error) {
      this.logService.error(`Error validating device: ${error}`);
      return false;
    }
  }

  // Setup periodic device validation checks
  private setupPeriodicDeviceValidation(): void {
    // Check device validity every 5 minutes
    const validationInterval = setInterval(async () => {
      const isValid = await this.validateDevice();
      if (!isValid) {
        this.logService.warn('Device validation failed during periodic check');
        this.zone.run(() => {
          this.deviceValidationError = true;
          this.playbackError = 'Device was removed from system';
        });
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Store the interval so we can clear it on destroy
    const subscription = new Subscription();
    subscription.add(() => clearInterval(validationInterval));
    this.subscriptions.push(subscription);
  }
}